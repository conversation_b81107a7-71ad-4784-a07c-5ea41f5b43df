@if(request()->has('print'))
    @extends('layouts.print')
@else
    @extends('layouts.accountant')
@endif

@section('title', 'Reçu de paiement #' . $payment->id)

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
    /* Variables */
    :root {
        --primary-color: #1E88E5;
        --primary-light: #BBDEFB;
        --primary-dark: #0D47A1;
        --secondary-color: #4CAF50;
        --secondary-light: #E8F5E9;
        --warning-color: #FF9800;
        --warning-light: #FFF3E0;
        --danger-color: #F44336;
        --danger-light: #FFEBEE;
        --info-color: #00BCD4;
        --info-light: #E0F7FA;
        --dark-color: #101828;
        --text-color: #344054;
        --text-light: #667085;
        --border-color: #EAECF0;
        --background-color: #F9FAFB;
        --gradient-blue: linear-gradient(135deg, #1E88E5, #0D47A1);
        --box-shadow: 0 8px 20px rgba(13, 71, 161, 0.08);
    }

    /* Styles généraux pour le format A5 */
    .receipt-container {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.5;
        color: var(--text-color);
        background-color: #f5f5f5;
        margin: 0;
        padding: 2rem 0;
    }

    /* Conteneur pour 2 reçus A5 côte à côte sur A4 paysage */
    .receipts-container {
        display: flex;
        flex-direction: row;
        justify-content: space-around;
        align-items: flex-start;
        gap: 10mm;
        width: 100%;
        max-width: none;
        margin: 0 auto;
        padding: 5mm;
        box-sizing: border-box;
    }

    /* Format A5 : 148mm x 210mm - 2 copies par page A4 */
    .receipt-page {
        width: 148mm;
        min-height: 210mm;
        background: white;
        position: relative;
        padding: 0;
        box-shadow: var(--box-shadow);
        overflow: hidden;
        border-radius: 8px;
        page-break-inside: avoid;
        flex: 0 0 148mm;
    }

    .receipt-content {
        width: 100%;
        height: 100%;
        background-color: #fff;
        position: relative;
        overflow: hidden;
        max-width: 148mm;
        margin: 0 auto;
    }
    
    /* En-tête avec dégradé moderne */
    .receipt-header {
        background: var(--gradient-blue);
        color: white;
        text-align: center;
        padding: 0.75rem 0.75rem 0.5rem;
        position: relative;
        overflow: hidden;
    }
    
    .receipt-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }
    
    /* Logo container avec effet de brillance */
    .receipt-logo-container {
        background-color: white;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 0.5rem;
        box-shadow: 0 4px 12px rgba(30, 136, 229, 0.15);
        position: relative;
        overflow: hidden;
        z-index: 10;
    }

    .receipt-logo {
        max-width: 40px;
        max-height: 40px;
        z-index: 2;
    }
    
    .receipt-title {
        font-size: 1rem;
        font-weight: 700;
        margin: 0.3rem 0 0.2rem;
        letter-spacing: 1px;
        position: relative;
        z-index: 2;
    }
    
    .receipt-number {
        font-size: 0.75rem;
        margin: 0;
        opacity: 0.9;
        font-weight: 500;
        position: relative;
        z-index: 2;
    }
    
    .receipt-date {
        font-size: 0.7rem;
        margin: 0.1rem 0 0;
        opacity: 0.8;
        position: relative;
        z-index: 2;
    }
    
    /* Corps du reçu avec design moderne - espacement minimal */
    .receipt-body {
        padding: 0.75rem 0.75rem;
        position: relative;
        background-color: #FAFBFF;
        background-image: none;
    }
    
    /* Conteneur pour les informations client et vente */
    .receipt-info-container {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        gap: 0.3rem;
        margin-bottom: 0.3rem;
    }
    
    .receipt-info-column {
        width: 49%;
    }
    
    /* Sections avec style de carte moderne - espacement minimal */
    .receipt-section {
        margin-bottom: 0.5rem;
        background-color: white;
        border-radius: 6px;
        padding: 0.5rem;
        box-shadow: 0 2px 6px rgba(13, 71, 161, 0.03);
        border: 1px solid rgba(187, 222, 251, 0.2);
        overflow: hidden;
        position: relative;
    }
    
    .receipt-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    }
    
    .receipt-section-title {
        font-size: 0.7rem;
        font-weight: 600;
        color: var(--primary-color);
        margin: 0 0 0.3rem;
        display: flex;
        align-items: center;
        gap: 0.3rem;
    }
    
    .receipt-section-title i {
        font-size: 0.6rem;
    }
    
    /* Lignes d'information */
    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 0.2rem;
        font-size: 0.65rem;
        line-height: 1.3;
    }
    
    .info-row:last-child {
        margin-bottom: 0;
    }
    
    .info-label {
        font-weight: 500;
        color: var(--text-light);
        flex: 0 0 45%;
    }
    
    .info-value {
        font-weight: 600;
        color: var(--text-color);
        text-align: right;
        flex: 1;
        word-break: break-word;
    }
    
    /* Badges de statut */
    .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.2rem;
        padding: 0.1rem 0.3rem;
        border-radius: 12px;
        font-size: 0.55rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-badge.completed {
        background-color: var(--secondary-light);
        color: var(--secondary-color);
    }

    .status-badge.pending {
        background-color: var(--warning-light);
        color: var(--warning-color);
    }

    .status-badge.partial {
        background-color: var(--info-light);
        color: var(--info-color);
    }

    /* Tableau des produits */
    .receipt-table {
        width: 100%;
        border-collapse: collapse;
        margin: 0.5rem 0;
        background-color: white;
        border-radius: 6px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .receipt-table th {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        padding: 0.4rem 0.3rem;
        text-align: left;
        font-size: 0.65rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .receipt-table td {
        padding: 0.4rem 0.3rem;
        border-bottom: 1px solid var(--border-color);
        font-size: 0.65rem;
        color: var(--text-color);
    }

    .receipt-table tbody tr:last-child td {
        border-bottom: none;
    }

    .receipt-table tbody tr:hover {
        background-color: rgba(30, 136, 229, 0.02);
    }

    /* Badge de méthode de paiement */
    .payment-method-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.2rem;
        padding: 0.1rem 0.3rem;
        background-color: var(--primary-light);
        color: var(--primary-color);
        border-radius: 12px;
        font-size: 0.55rem;
        font-weight: 600;
    }
    
    /* Résumé de paiement */
    .payment-summary {
        background: linear-gradient(135deg, #f8fafc, #e2e8f0);
        border-radius: 8px;
        padding: 0.5rem;
        border: 1px solid var(--border-color);
    }
    
    .payment-summary-title {
        font-size: 0.7rem;
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 0.3rem;
        display: flex;
        align-items: center;
        gap: 0.3rem;
    }
    
    .payment-summary-content {
        background-color: white;
        border-radius: 6px;
        padding: 0.4rem;
    }
    
    .payment-summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.2rem;
        font-size: 0.6rem;
    }
    
    .payment-summary-row:last-child {
        margin-bottom: 0;
        padding-top: 0.2rem;
        border-top: 1px solid var(--border-color);
        font-weight: 700;
        color: var(--primary-color);
    }
    
    .payment-summary-value {
        font-weight: 600;
        color: var(--text-color);
    }
    
    /* Signatures */
    .receipt-signatures {
        display: flex;
        justify-content: space-between;
        gap: 0.3rem;
        margin: 0.5rem 0;
    }
    
    .signature-box {
        flex: 1;
        text-align: center;
    }
    
    .signature-line {
        height: 1px;
        background-color: var(--border-color);
        margin-bottom: 0.2rem;
    }
    
    .signature-title {
        font-size: 0.55rem;
        color: var(--text-light);
        margin: 0;
        font-weight: 500;
    }
    
    /* Cachet d'entreprise */
    .company-stamp {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 2rem;
        margin-bottom: 0.2rem;
    }
    
    .stamp-circle {
        width: 1.8rem;
        height: 1.8rem;
        border: 2px solid var(--primary-color);
        border-radius: 50%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background-color: rgba(30, 136, 229, 0.05);
    }
    
    .stamp-text {
        font-size: 0.45rem;
        font-weight: 700;
        color: var(--primary-color);
        line-height: 1;
    }
    
    .stamp-subtext {
        font-size: 0.35rem;
        color: var(--primary-color);
        line-height: 1;
    }
    
    /* Note et QR Code */
    .receipt-note {
        background-color: var(--info-light);
        border-left: 3px solid var(--info-color);
        padding: 0.3rem;
        margin: 0.3rem 0;
        border-radius: 0 4px 4px 0;
        font-size: 0.6rem;
        display: flex;
        align-items: flex-start;
        gap: 0.3rem;
    }
    
    .receipt-note i {
        color: var(--info-color);
        margin-top: 0.1rem;
        flex-shrink: 0;
    }
    
    .receipt-qr {
        text-align: center;
        margin: 0.3rem 0;
    }
    
    .receipt-qr img {
        width: 2.5rem;
        height: 2.5rem;
        border: 1px solid var(--border-color);
        border-radius: 4px;
    }
    
    .receipt-qr-text {
        font-size: 0.5rem;
        color: var(--text-light);
        margin: 0.2rem 0 0;
    }
    
    /* Termes et conditions */
    .terms-conditions {
        background-color: #f8f9fa;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        padding: 0.3rem;
        margin-top: 0.3rem;
        text-align: center;
    }
    
    .terms-conditions-title {
        font-size: 0.6rem;
        font-weight: 600;
        color: var(--text-color);
        margin: 0 0 0.2rem;
    }
    
    .terms-conditions p {
        font-size: 0.5rem;
        color: var(--text-light);
        margin: 0;
        line-height: 1.3;
    }
    
    /* Styles d'impression */
    @media print {
        @page {
            size: 297mm 210mm;
            margin: 5mm;
            orientation: landscape;
        }

        html {
            width: 297mm !important;
            height: 210mm !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        body {
            width: 297mm !important;
            height: 210mm !important;
            margin: 0 !important;
            padding: 0 !important;
            font-size: 9px !important;
            line-height: 1.1 !important;
            overflow: hidden !important;
            transform-origin: top left !important;
        }

        .no-print,
        .sidebar,
        .navbar,
        .breadcrumb,
        .action-buttons,
        nav,
        .main-sidebar,
        .content-wrapper > .content-header {
            display: none !important;
        }

        .receipt-container {
            background-color: white !important;
            padding: 0 !important;
            margin: 0 !important;
            width: 287mm !important;
            height: 200mm !important;
            position: relative !important;
            overflow: visible !important;
        }

        .receipts-container {
            display: flex !important;
            flex-direction: row !important;
            justify-content: space-between !important;
            align-items: flex-start !important;
            gap: 7mm !important;
            width: 287mm !important;
            height: 200mm !important;
            margin: 0 !important;
            padding: 5mm !important;
            box-sizing: border-box !important;
        }

        .receipt-page {
            box-shadow: none !important;
            border-radius: 0 !important;
            width: 140mm !important;
            max-width: 140mm !important;
            height: 190mm !important;
            max-height: 190mm !important;
            padding: 2mm !important;
            page-break-inside: avoid !important;
            flex: 0 0 140mm !important;
            margin: 0 !important;
            overflow: hidden !important;
            border: 1px solid #ccc !important;
            box-sizing: border-box !important;
            background: white !important;
        }

        body {
            background-color: white !important;
            margin: 0 !important;
            padding: 0 !important;
            font-size: 10px !important;
            line-height: 1.2 !important;
            width: 297mm !important;
            height: 210mm !important;
            overflow: hidden !important;
        }

        * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
        }

        .container-fluid {
            padding: 0 !important;
            margin: 0 !important;
            max-width: none !important;
            width: 297mm !important;
            height: 210mm !important;
        }

        .content-wrapper {
            margin: 0 !important;
            padding: 0 !important;
            width: 297mm !important;
            height: 210mm !important;
        }

        /* Force l'aperçu d'impression en paysage */
        @media screen {
            .receipt-container {
                transform-origin: top left !important;
                transform: none !important;
            }
        }

        /* Optimisation des éléments pour 2 A5 sur A4 paysage */
        .receipt-header {
            padding: 0.2rem !important;
            margin-bottom: 0.3rem !important;
        }

        .receipt-title {
            font-size: 1rem !important;
            margin: 0.2rem 0 !important;
        }

        .receipt-number,
        .receipt-date {
            font-size: 0.7rem !important;
            margin: 0.1rem 0 !important;
        }

        .receipt-body {
            padding: 0.2rem !important;
        }

        .receipt-section {
            margin-bottom: 0.2rem !important;
            padding: 0.2rem !important;
        }

        .receipt-section-title {
            font-size: 0.7rem !important;
            margin-bottom: 0.2rem !important;
        }

        .receipt-table {
            margin: 0.2rem 0 !important;
            font-size: 0.6rem !important;
        }

        .receipt-table th,
        .receipt-table td {
            padding: 0.1rem !important;
            font-size: 0.6rem !important;
        }

        .info-row {
            margin-bottom: 0.1rem !important;
        }

        .info-label,
        .info-value {
            font-size: 0.65rem !important;
        }

        .payment-summary-row {
            font-size: 0.6rem !important;
            margin-bottom: 0.1rem !important;
        }

        .receipt-logo-container {
            width: 25px !important;
            height: 25px !important;
            margin: 0 auto 0.2rem !important;
            padding: 1px !important;
        }

        .receipt-logo {
            width: 23px !important;
            height: 23px !important;
        }

        .receipt-signatures {
            margin-top: 0.3rem !important;
            gap: 0.2rem !important;
        }

        .signature-box {
            padding: 0.2rem !important;
        }

        .signature-line {
            height: 10px !important;
            margin-bottom: 0.1rem !important;
        }

        .signature-title {
            font-size: 0.6rem !important;
        }

        .receipt-qr {
            margin-top: 0.3rem !important;
        }

        .receipt-qr img {
            width: 40px !important;
            height: 40px !important;
        }

        .receipt-qr-text {
            font-size: 0.5rem !important;
        }

        .terms-conditions {
            margin-top: 0.2rem !important;
        }

        .terms-conditions-title {
            font-size: 0.6rem !important;
        }

        .terms-conditions p {
            font-size: 0.5rem !important;
            line-height: 1.1 !important;
        }

        .company-stamp {
            width: 30px !important;
            height: 30px !important;
        }

        .stamp-text {
            font-size: 0.4rem !important;
        }

        .stamp-subtext {
            font-size: 0.3rem !important;
        }
    }
    
    /* Styles pour les boutons d'action */
    .action-buttons {
        margin-bottom: 2rem;
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .btn-print {
        background: linear-gradient(135deg, #1E88E5, #0D47A1);
        border: none;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(30, 136, 229, 0.3);
    }
    
    .btn-print:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(30, 136, 229, 0.4);
        color: white;
    }
    
    .btn-back {
        background: #6c757d;
        border: none;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-back:hover {
        background: #5a6268;
        transform: translateY(-2px);
        color: white;
    }
</style>
@endpush

@section('content')
@if(!request()->has('print'))
<div class="container-fluid px-4">
    <!-- Boutons d'action uniquement -->
    <div class="d-flex justify-content-end mb-4 no-print">
        <div class="action-buttons">
            <a href="{{ route('accountant.recoveries.show', $payment->sale_id) }}" class="btn btn-back">
                <i class="fas fa-arrow-left me-1"></i> Retour au recouvrement
            </a>
            <button class="btn btn-print" onclick="printReceipts()">
                <i class="fas fa-print me-1"></i> Imprimer 2 reçus A5 sur A4 paysage
            </button>
        </div>
    </div>
@endif
    
    <!-- Conteneur pour 2 reçus A5 sur feuille A4 -->
    <div class="receipt-container">
        <div class="receipts-container">
            <!-- Premier reçu A5 -->
            <div class="receipt-page">
            <div class="receipt-content">
                <div class="receipt-header">
                    <div class="receipt-logo-container">
                        <img src="{{ asset('assets/images/logo_gradis.png') }}" alt="Logo" class="receipt-logo">
                    </div>
                    <h1 class="receipt-title">REÇU DE PAIEMENT</h1>
                    <p class="receipt-number"># {{ $payment->receipt_number ?? 'REC-' . str_pad($payment->id, 6, '0', STR_PAD_LEFT) }}</p>
                    <p class="receipt-date">{{ $payment->payment_date->format('d/m/Y H:i') }}</p>
                </div>
                
                <div class="receipt-body">
                    <div class="receipt-info-container">
                        <div class="receipt-info-column">
                            <div class="receipt-section">
                                <h5 class="receipt-section-title"><i class="fas fa-user"></i> Informations client</h5>
                                <div class="info-row">
                                    <div class="info-label">Nom:</div>
                                    <div class="info-value">{{ $payment->sale->customer_name }}</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">Téléphone:</div>
                                    <div class="info-value">{{ $payment->sale->customer_phone }}</div>
                                </div>

                                <div class="info-row">
                                    <div class="info-label">Adresse:</div>
                                    <div class="info-value">{{ $payment->sale->customer_address }}</div>
                                </div>
                            </div>
                            
                            <div class="receipt-section">
                                <h5 class="receipt-section-title"><i class="fas fa-shopping-cart"></i> Informations vente</h5>
                                <div class="info-row">
                                    <div class="info-label">Référence:</div>
                                    <div class="info-value">{{ $payment->sale->invoice_number ?? 'VNT-' . str_pad($payment->sale->id, 6, '0', STR_PAD_LEFT) }}</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">Date de vente:</div>
                                    <div class="info-value">{{ $payment->sale->created_at->format('d/m/Y') }}</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">Statut du paiement:</div>
                                    <div class="info-value">
                                        @if($payment->status === 'completed')
                                            <span class="status-badge completed"><i class="fas fa-check-circle"></i> Payé</span>
                                        @elseif($payment->status === 'pending')
                                            <span class="status-badge pending"><i class="fas fa-clock"></i> En attente</span>
                                        @elseif($payment->status === 'cancelled')
                                            <span class="status-badge pending"><i class="fas fa-times-circle"></i> Annulé</span>
                                        @else
                                            <span class="status-badge completed"><i class="fas fa-check-circle"></i> Payé</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="receipt-section">
                    <h5 class="receipt-section-title"><i class="fas fa-box"></i> Détails du produit</h5>
                    <table class="receipt-table">
                        <thead>
                            <tr>
                                <th>Produit</th>
                                <th>Quantité</th>
                                <th>Prix unitaire</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    @if($payment->sale->product)
                                        {{ $payment->sale->product->name }}
                                    @elseif($payment->sale->supply && $payment->sale->supply->details->isNotEmpty())
                                        {{ $payment->sale->supply->details->first()->product->name }}
                                    @else
                                        N/A
                                    @endif
                                </td>
                                <td>{{ number_format($payment->sale->quantity, 2, ',', ' ') }} T</td>
                                <td>{{ number_format($payment->sale->unit_price, 0, ',', ' ') }} FCFA</td>
                                <td>{{ number_format($payment->sale->total_amount, 0, ',', ' ') }} FCFA</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="receipt-info-container">
                    <div class="receipt-info-column">
                        <div class="receipt-section">
                            <h5 class="receipt-section-title"><i class="fas fa-credit-card"></i> Détails du paiement</h5>
                            <div class="info-row">
                                <div class="info-label">Mode de paiement:</div>
                                <div class="info-value">
                                    <span class="payment-method-badge">
                                        @switch($payment->payment_method)
                                            @case('cash')
                                                <i class="fas fa-money-bill-wave"></i> Espèces
                                                @break
                                            @case('bank_transfer')
                                                <i class="fas fa-university"></i> Virement bancaire
                                                @break
                                            @case('check')
                                                <i class="fas fa-money-check"></i> Chèque
                                                @break
                                            @case('mobile_money')
                                                <i class="fas fa-mobile-alt"></i> Mobile Money
                                                @break
                                            @default
                                                <i class="fas fa-money-bill-wave"></i> Espèces
                                        @endswitch
                                    </span>
                                </div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Montant payé:</div>
                                <div class="info-value">{{ number_format($payment->amount, 0, ',', ' ') }} FCFA</div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Date de paiement:</div>
                                <div class="info-value">{{ $payment->payment_date->format('d/m/Y H:i') }}</div>
                            </div>
                            @if($payment->notes)
                            <div class="info-row">
                                <div class="info-label">Notes:</div>
                                <div class="info-value">{{ $payment->notes }}</div>
                            </div>
                            @endif
                        </div>
                    </div>
                    <div class="receipt-info-column">
                        <div class="payment-summary">
                            <div class="payment-summary-title">
                                <i class="fas fa-calculator"></i> Résumé financier
                            </div>
                            <div class="payment-summary-content">
                                <div class="payment-summary-row">
                                    <div>Montant total de la vente:</div>
                                    <div class="payment-summary-value">{{ number_format($payment->sale->total_amount, 0, ',', ' ') }} FCFA</div>
                                </div>
                                <div class="payment-summary-row">
                                    <div>Montant déjà payé:</div>
                                    <div class="payment-summary-value">{{ number_format($payment->sale->amount_paid - $payment->amount, 0, ',', ' ') }} FCFA</div>
                                </div>
                                <div class="payment-summary-row">
                                    <div>Ce paiement:</div>
                                    <div class="payment-summary-value">{{ number_format($payment->amount, 0, ',', ' ') }} FCFA</div>
                                </div>
                                <div class="payment-summary-row">
                                    <div>Solde restant:</div>
                                    <div class="payment-summary-value">{{ number_format($payment->sale->total_amount - $payment->sale->amount_paid, 0, ',', ' ') }} FCFA</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                    
                    <div class="receipt-signatures">
                        <div class="signature-box">
                            <div class="signature-line"></div>
                            <p class="signature-title">Signature du caissier</p>
                        </div>
                        <div class="signature-box stamp">
                            <div class="company-stamp">
                                <div class="stamp-circle">
                                    <div class="stamp-text">GRADIS</div>
                                    <div class="stamp-subtext">OFFICIEL</div>
                                </div>
                            </div>
                            <p class="signature-title">Cachet de l'entreprise</p>
                        </div>
                        <div class="signature-box">
                            <div class="signature-line"></div>
                            <p class="signature-title">Signature du client</p>
                        </div>
                    </div>
                    
                    <div class="receipt-note">
                        <i class="fas fa-info-circle"></i>
                        <div>
                            <strong>Info :</strong> Preuve officielle de paiement. À conserver pour réclamations.
                        </div>
                    </div>
                    
                    <div class="receipt-qr">
                        <img src="https://api.qrserver.com/v1/create-qr-code/?size=100x100&data={{ urlencode(route('accountant.cashier-receipt', $payment->id)) }}" alt="QR Code">
                        <p class="receipt-qr-text">Scannez pour vérifier l'authenticité</p>
                    </div>
                    
                    <div class="terms-conditions">
                        <p class="terms-conditions-title">Termes et Conditions</p>
                        <p>Valable avec cachet. Remboursement sous 7 jours. Réclamation avec reçu original uniquement.</p>
                    </div>
                </div>
            </div>


        </div>
    </div>
@if(!request()->has('print'))
</div>
@endif
@endsection

@push('scripts')
<script>
    // Fonction d'impression optimisée pour paysage A4
    function printReceipts() {
        // Ajouter une classe pour l'impression
        document.body.classList.add('printing');

        // Créer un style spécifique pour forcer le paysage
        const printStyle = document.createElement('style');
        printStyle.id = 'landscape-print-style';
        printStyle.textContent = `
            @media print {
                @page {
                    size: 297mm 210mm;
                    margin: 5mm;
                    orientation: landscape;
                }
                html {
                    width: 297mm !important;
                    height: 210mm !important;
                    margin: 0 !important;
                    padding: 0 !important;
                }
                body {
                    width: 297mm !important;
                    height: 210mm !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    overflow: hidden !important;
                    font-size: 9px !important;
                }
                .receipt-container {
                    width: 287mm !important;
                    height: 200mm !important;
                    margin: 0 !important;
                    padding: 0 !important;
                }
                .receipts-container {
                    width: 287mm !important;
                    height: 200mm !important;
                    padding: 5mm !important;
                    display: flex !important;
                    flex-direction: row !important;
                    justify-content: space-between !important;
                    gap: 7mm !important;
                    box-sizing: border-box !important;
                }
                .receipt-page {
                    width: 140mm !important;
                    height: 190mm !important;
                    flex: 0 0 140mm !important;
                }
            }
        `;
        document.head.appendChild(printStyle);

        // Message d'instruction pour l'utilisateur
        alert('IMPORTANT: Dans l\'aperçu d\'impression, assurez-vous de sélectionner l\'orientation PAYSAGE pour voir les 2 reçus côte à côte.');

        // Attendre un peu puis lancer l'impression
        setTimeout(() => {
            window.print();
        }, 200);
    }

    // Événements d'impression
    window.addEventListener('beforeprint', function() {
        document.body.classList.add('printing');
        console.log('Impression en mode paysage A4');
    });

    window.addEventListener('afterprint', function() {
        document.body.classList.remove('printing');
        // Nettoyer le style d'impression
        const printStyle = document.getElementById('landscape-print-style');
        if (printStyle) {
            printStyle.remove();
        }
    });

    // Raccourci clavier pour imprimer
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            printReceipts();
        }
    });

    // Mise à jour du bouton d'impression au chargement de la page
    document.addEventListener('DOMContentLoaded', function() {
        const printButton = document.querySelector('.btn-print');
        if (printButton) {
            printButton.onclick = function(e) {
                e.preventDefault();
                printReceipts();
            };
        }
    });
</script>
@endpush
