@extends('layouts.accountant')

@section('title', 'Enregistrer un Paiement')

@push('styles')
<!-- Animate.css pour les animations -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<style>
    /* Variables */
    :root {
        --primary-color: #2196F3;
        --primary-light: #BBDEFB;
        --primary-dark: #1976D2;
        --success-color: #4CAF50;
        --success-light: #E8F5E9;
        --warning-color: #FF9800;
        --warning-light: #FFF3E0;
        --danger-color: #F44336;
        --danger-light: #FFEBEE;
        --info-color: #00BCD4;
        --info-light: #E0F7FA;
        --dark-color: #101828;
        --text-color: #344054;
        --border-color: #EAECF0;
        --background-color: #F9FAFB;
        --card-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    }
    
    /* Styles généraux */
    body {
        background-color: var(--background-color);
        color: var(--text-color);
    }
    
    .container-fluid {
        padding: 2rem 2.5rem;
        max-width: 1600px;
        margin: 0 auto;
        animation: fadeIn 0.5s ease-out;
    }
    
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--border-color);
    }
    
    .page-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--dark-color);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin: 0;
    }
    
    .page-title i {
        color: var(--primary-color);
    }
    
    /* Cartes de paiement */
    .payment-card {
        transition: all 0.3s ease;
        cursor: pointer;
        border: 1px solid var(--border-color);
        border-radius: 12px;
        background-color: white;
        box-shadow: 0 1px 3px rgba(16, 24, 40, 0.1);
        height: 100%;
    }
    
    .payment-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--card-shadow);
    }
    
    .payment-card.selected {
        border-color: var(--primary-color);
        background-color: var(--primary-light);
        box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.2);
    }
    
    .payment-method-icon {
        font-size: 2.5rem;
        margin-bottom: 0.75rem;
        transition: transform 0.2s ease;
    }
    
    .payment-card:hover .payment-method-icon {
        transform: scale(1.1);
    }
    
    /* Éléments d'échéancier */
    .schedule-item {
        border-left: 4px solid #ccc;
        padding: 1rem 1.25rem;
        margin-bottom: 1rem;
        transition: all 0.2s ease;
        background-color: white;
        border-radius: 0 8px 8px 0;
        box-shadow: 0 1px 3px rgba(16, 24, 40, 0.1);
    }
    
    .schedule-item:hover {
        border-left-color: var(--primary-color);
        box-shadow: 0 4px 8px rgba(16, 24, 40, 0.1);
    }
    
    .schedule-item.overdue {
        border-left-color: var(--danger-color);
        background-color: var(--danger-light);
    }
    
    .schedule-item.paid {
        border-left-color: var(--success-color);
        background-color: var(--success-light);
    }
    
    /* Résumé de paiement */
    .payment-summary {
        background-color: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: var(--card-shadow);
        border: 1px solid var(--border-color);
    }
    
    /* Cartes */
    .card {
        border-radius: 12px;
        border: 1px solid var(--border-color);
        box-shadow: var(--card-shadow);
        overflow: hidden;
        margin-bottom: 1.5rem;
        background-color: white;
    }
    
    .card-header {
        padding: 1rem 1.5rem;
        background-color: #FCFCFD;
        border-bottom: 1px solid var(--border-color);
        font-weight: 600;
        color: var(--dark-color);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .card-header i {
        color: var(--primary-color);
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    /* Formulaires */
    .form-label {
        font-weight: 500;
        color: var(--dark-color);
        margin-bottom: 0.5rem;
    }
    
    .form-control {
        border-radius: 8px;
        border: 1px solid var(--border-color);
        padding: 0.625rem 1rem;
        font-size: 0.875rem;
        transition: all 0.2s ease;
    }
    
    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.1);
    }
    
    /* Boutons */
    .btn {
        border-radius: 8px;
        padding: 0.625rem 1.25rem;
        font-weight: 500;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }
    
    .btn-primary:hover {
        background-color: var(--primary-dark);
        border-color: var(--primary-dark);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(33, 150, 243, 0.2);
    }
    
    .btn-outline-secondary {
        border-color: var(--border-color);
        color: var(--text-color);
    }
    
    .btn-outline-secondary:hover {
        background-color: #F9FAFB;
        color: var(--dark-color);
    }
    
    /* Alertes */
    .alert {
        border-radius: 12px;
        border: 1px solid transparent;
        padding: 1rem 1.25rem;
    }
    
    .alert-info {
        background-color: var(--info-light);
        border-color: var(--info-color);
        color: var(--info-color);
    }
    
    /* Animations */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .animate-fade-in {
        animation: fadeIn 0.5s ease-out forwards;
    }
    
    .payment-progress {
        height: 10px;
        border-radius: 5px;
        margin: 1rem 0;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- En-tête de page avec titre -->
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-cash-register"></i>
            <span>Enregistrer un paiement</span>
        </h1>
        <div class="breadcrumb-navigation">
            <a href="{{ route('accountant.dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-home"></i> Tableau de bord
            </a>
            <a href="{{ route('accountant.recoveries.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-list"></i> Liste des recouvrements
            </a>
        </div>
    </div>

    @include('partials.alerts')

    <div class="row">
        <!-- Informations sur la vente -->
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-info-circle me-1"></i>
                    Détails du recouvrement
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h5 class="d-flex align-items-center mb-3" style="color: var(--primary-color);">
                            <i class="fas fa-user-circle me-2"></i> Client
                        </h5>
                        <div class="info-item d-flex justify-content-between mb-2">
                            <span class="text-muted">Nom:</span>
                            <span class="fw-medium">{{ $sale->customer_name }}</span>
                        </div>
                        @if($sale->customer_phone)
                            <div class="info-item d-flex justify-content-between mb-2">
                                <span class="text-muted">Téléphone:</span>
                                <a href="tel:{{ $sale->customer_phone }}" class="text-decoration-none fw-medium">{{ $sale->customer_phone }}</a>
                            </div>
                        @endif
                    </div>
                    
                    <div class="mb-4">
                        <h5 class="d-flex align-items-center mb-3" style="color: var(--primary-color);">
                            <i class="fas fa-file-invoice me-2"></i> Facture
                        </h5>
                        <div class="info-item d-flex justify-content-between mb-2">
                            <span class="text-muted">Numéro:</span>
                            <span class="fw-medium">#{{ $sale->invoice_number }}</span>
                        </div>
                        <div class="info-item d-flex justify-content-between mb-2">
                            <span class="text-muted">Date:</span>
                            <span class="fw-medium">{{ $sale->created_at->format('d/m/Y') }}</span>
                        </div>
                    </div>
                    
                    <div class="payment-summary">
                        <h5 class="d-flex align-items-center mb-3" style="color: var(--primary-color);">
                            <i class="fas fa-file-invoice-dollar me-2"></i> Résumé financier
                        </h5>
                        <div class="info-item d-flex justify-content-between mb-3">
                            <span class="text-muted">Montant total:</span>
                            <span class="fw-bold" style="font-size: 1.1rem;">{{ number_format($totalAmount, 0, ',', ' ') }} FCFA</span>
                        </div>
                        <div class="info-item d-flex justify-content-between mb-3">
                            <span class="text-muted">Déjà payé:</span>
                            <span class="fw-medium text-success">{{ number_format($paidAmount, 0, ',', ' ') }} FCFA</span>
                        </div>
                        <div class="info-item d-flex justify-content-between mb-3" style="padding-top: 0.75rem; border-top: 1px solid var(--border-color);">
                            <span class="fw-medium">Reste à payer:</span>
                            <span class="fw-bold text-danger" style="font-size: 1.2rem;">{{ number_format($remainingAmount, 0, ',', ' ') }} FCFA</span>
                        </div>
                        
                        <div class="mt-4">
                            <label class="form-label d-flex align-items-center">
                                <i class="fas fa-chart-pie text-primary me-2"></i>
                                Progression du paiement
                            </label>
                            <div class="progress" style="height: 10px; border-radius: 10px; background-color: #F2F4F7;">
                                @php
                                    $percentage = $paymentPercentage;
                                @endphp
                                <div class="progress-bar" role="progressbar" 
                                    style="width: {{ $percentage }}%; background: linear-gradient(90deg, #4CAF50, #8BC34A); border-radius: 10px;" 
                                    aria-valuenow="{{ $percentage }}" aria-valuemin="0" aria-valuemax="100">
                                    {{ round($percentage) }}%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Formulaire de paiement -->
        <div class="col-md-8">
            <div class="card mb-4 animate-fade-in" style="animation-delay: 0.2s;">
                <div class="card-header">
                    <i class="fas fa-cash-register"></i>
                    Enregistrer un paiement
                </div>
                <div class="card-body">
                    <form id="paymentForm" action="{{ route('accountant.recoveries.update', $sale->id) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label for="amount" class="form-label d-flex align-items-center">
                                        <i class="fas fa-coins text-primary me-2"></i>
                                        Montant <span class="text-danger ms-1">*</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control @error('amount') is-invalid @enderror" 
                                            id="amount" name="amount" step="0.01" min="0" max="{{ $remainingAmount }}" 
                                            value="{{ old('amount', $remainingAmount) }}" required>
                                        <span class="input-group-text">FCFA</span>
                                    </div>
                                    <small class="form-text text-muted">Montant maximum: {{ number_format($remainingAmount, 0, ',', ' ') }} FCFA</small>
                                    @error('amount')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-4">
                                    <label for="payment_date" class="form-label d-flex align-items-center">
                                        <i class="fas fa-calendar-alt text-primary me-2"></i>
                                        Date du paiement <span class="text-danger ms-1">*</span>
                                    </label>
                                    <input type="date" class="form-control @error('payment_date') is-invalid @enderror" 
                                        id="payment_date" name="payment_date" value="{{ old('payment_date', date('Y-m-d')) }}" required>
                                    @error('payment_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-4">
                                    <label for="notes" class="form-label d-flex align-items-center">
                                        <i class="fas fa-sticky-note text-primary me-2"></i>
                                        Notes / Commentaires
                                    </label>
                                    <textarea class="form-control @error('notes') is-invalid @enderror" id="notes" name="notes" rows="3">{{ old('notes') }}</textarea>
                                    @error('notes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <label class="form-label d-flex align-items-center mb-3">
                                    <i class="fas fa-credit-card text-primary me-2"></i>
                                    Mode de paiement <span class="text-danger ms-1">*</span>
                                </label>
                                
                                <div class="payment-methods">
                                    <div class="row g-3">
                                        <div class="col-6">
                                            <div class="payment-card text-center p-3 rounded selected" data-method="cash">
                                                <i class="fas fa-money-bill-wave payment-method-icon text-success"></i>
                                                <h6 class="mb-0 fw-medium">Espèces</h6>
                                                <input type="radio" name="payment_method" value="cash" class="d-none" {{ old('payment_method', 'cash') == 'cash' ? 'checked' : '' }}>
                                            </div>
                                        </div>

                                        <div class="col-6">
                                            <div class="payment-card text-center p-3 rounded" data-method="bank_transfer">
                                                <i class="fas fa-university payment-method-icon text-primary"></i>
                                                <h6 class="mb-0 fw-medium">Virement</h6>
                                                <input type="radio" name="payment_method" value="bank_transfer" class="d-none" {{ old('payment_method') == 'bank_transfer' ? 'checked' : '' }}>
                                            </div>
                                        </div>

                                        <div class="col-6">
                                            <div class="payment-card text-center p-3 rounded" data-method="check">
                                                <i class="fas fa-money-check-alt payment-method-icon text-info"></i>
                                                <h6 class="mb-0 fw-medium">Chèque</h6>
                                                <input type="radio" name="payment_method" value="check" class="d-none" {{ old('payment_method') == 'check' ? 'checked' : '' }}>
                                            </div>
                                        </div>

                                        <div class="col-6">
                                            <div class="payment-card text-center p-3 rounded" data-method="mobile_money">
                                                <i class="fas fa-mobile-alt payment-method-icon text-warning"></i>
                                                <h6 class="mb-0 fw-medium">Mobile Money</h6>
                                                <input type="radio" name="payment_method" value="mobile_money" class="d-none" {{ old('payment_method') == 'mobile_money' ? 'checked' : '' }}>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="alert alert-info mt-4" id="paymentMethodInfo" style="border-radius: 12px; border-left: 4px solid var(--info-color);">
                                    <h6 class="alert-heading d-flex align-items-center">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <span>Paiement en espèces</span>
                                    </h6>
                                    <p class="mb-0">Assurez-vous de compter correctement les espèces et de délivrer un reçu au client.</p>
                                </div>
                                

                                <div class="reference-field mt-4" style="display: none;">
                                    <label for="reference_number" class="form-label d-flex align-items-center">
                                        <i class="fas fa-hashtag text-primary me-2"></i>
                                        Numéro de référence <span class="text-danger ms-1">*</span>
                                    </label>
                                    <input type="text" class="form-control @error('reference_number') is-invalid @enderror"
                                        id="reference_number" name="reference_number" value="{{ old('reference_number') }}"
                                        placeholder="Saisissez le numéro de référence">
                                    <small class="form-text text-muted">
                                        Ce champ est requis pour les paiements par virement, chèque ou mobile money.
                                    </small>
                                    @error('reference_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4 pt-3" style="border-top: 1px solid var(--border-color);">
                            <a href="{{ route('accountant.recoveries.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i> Retour
                            </a>
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-info" onclick="debugPaymentSelection()">
                                    <i class="fas fa-bug me-2"></i> Debug
                                </button>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-2"></i> Enregistrer le paiement
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Historique des paiements avec possibilité d'édition -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-history me-2"></i>
                        Historique des paiements
                    </div>
                    @if(count($payments) > 0)
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleReceiptEditor()">
                            <i class="fas fa-edit me-1"></i> Éditer les reçus
                        </button>
                    @endif
                </div>
                <div class="card-body">
                    @if(count($payments) > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Montant</th>
                                        <th>Méthode</th>
                                        <th>Référence</th>
                                        <th>Statut</th>
                                        <th>Commentaire</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($payments as $payment)
                                    <tr data-payment-id="{{ $payment->id }}">
                                        <td>{{ $payment->created_at->format('d/m/Y H:i') }}</td>
                                        <td class="fw-medium">{{ number_format($payment->amount, 0, ',', ' ') }} FCFA</td>
                                        <td>
                                            @if($payment->payment_method == 'cash')
                                                <span class="badge bg-success"><i class="fas fa-money-bill-wave me-1"></i>Espèces</span>
                                            @elseif($payment->payment_method == 'bank_transfer')
                                                <span class="badge bg-primary"><i class="fas fa-university me-1"></i>Virement</span>
                                            @elseif($payment->payment_method == 'check')
                                                <span class="badge bg-info"><i class="fas fa-money-check-alt me-1"></i>Chèque</span>
                                            @elseif($payment->payment_method == 'mobile_money')
                                                <span class="badge bg-warning"><i class="fas fa-mobile-alt me-1"></i>Mobile Money</span>
                                            @else
                                                <span class="badge bg-secondary">{{ $payment->payment_method }}</span>
                                            @endif
                                        </td>
                                        <td>{{ $payment->reference_number ?? '-' }}</td>
                                        <td>
                                            @if($payment->status == 'completed')
                                                <span class="badge bg-success"><i class="fas fa-check-circle me-1"></i>Complété</span>
                                            @elseif($payment->status == 'pending')
                                                <span class="badge bg-warning"><i class="fas fa-clock me-1"></i>En attente</span>
                                            @else
                                                <span class="badge bg-danger"><i class="fas fa-times-circle me-1"></i>Échoué</span>
                                            @endif
                                        </td>
                                        <td>{{ $payment->notes ?? '-' }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary"
                                                        onclick="viewReceipt({{ $payment->id }})"
                                                        title="Voir le reçu">
                                                    <i class="fas fa-receipt"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-info"
                                                        onclick="editPayment({{ $payment->id }})"
                                                        title="Éditer le paiement">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-success"
                                                        onclick="printReceipt({{ $payment->id }})"
                                                        title="Imprimer le reçu">
                                                    <i class="fas fa-print"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <p class="mb-0">Aucun paiement enregistré pour cette vente.</p>
                            <small class="text-muted">Les paiements s'afficheront ici une fois enregistrés</small>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Modal d'édition de reçu -->
            <div class="modal fade" id="receiptEditModal" tabindex="-1" aria-labelledby="receiptEditModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="receiptEditModalLabel">
                                <i class="fas fa-edit me-2"></i>Éditer le reçu de paiement
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form id="receiptEditForm">
                                <input type="hidden" id="edit_payment_id" name="payment_id">

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="edit_amount" class="form-label">Montant</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="edit_amount" name="amount" step="0.01" min="0">
                                                <span class="input-group-text">FCFA</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="edit_payment_date" class="form-label">Date du paiement</label>
                                            <input type="date" class="form-control" id="edit_payment_date" name="payment_date">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="edit_payment_method" class="form-label">Méthode de paiement</label>
                                            <select class="form-select" id="edit_payment_method" name="payment_method">
                                                <option value="cash">Espèces</option>
                                                <option value="bank_transfer">Virement bancaire</option>
                                                <option value="check">Chèque</option>
                                                <option value="mobile_money">Mobile Money</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="edit_reference_number" class="form-label">Numéro de référence</label>
                                            <input type="text" class="form-control" id="edit_reference_number" name="reference_number" placeholder="Optionnel">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="edit_notes" class="form-label">Notes / Commentaires</label>
                                    <textarea class="form-control" id="edit_notes" name="notes" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>Annuler
                            </button>
                            <button type="button" class="btn btn-primary" onclick="savePaymentEdit()">
                                <i class="fas fa-save me-1"></i>Sauvegarder
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- Inclure le système de correction des méthodes de paiement -->
<script src="{{ asset('js/payment-method-fix.js') }}"></script>

<script>
    $(document).ready(function() {
        // Initialiser le système de correction des méthodes de paiement
        if (typeof PaymentMethodFix !== 'undefined') {
            PaymentMethodFix.init();
            console.log('✅ Système de correction des méthodes de paiement initialisé');
        }

        // Initialiser les tooltips Bootstrap
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
        
        // Gestion améliorée des cartes de paiement
        $('.payment-card').on('click', function() {
            const method = $(this).data('method');
            console.log('🔄 Sélection de la méthode:', method);

            // Retirer la sélection de toutes les cartes
            $('.payment-card').removeClass('selected');

            // Ajouter la sélection à la carte cliquée
            $(this).addClass('selected');

            // Cocher le radio button correspondant
            const radioButton = $('input[name="payment_method"][value="' + method + '"]');
            radioButton.prop('checked', true);

            // Déclencher l'événement change pour la validation
            radioButton.trigger('change');

            // Mettre à jour l'affichage des informations
            updatePaymentMethodInfo(method);

            // Gérer le champ de référence
            toggleReferenceField(method);

            console.log('✅ Méthode sélectionnée:', method);
        });

        // Gestion des événements sur les radio buttons
        $('input[name="payment_method"]').on('change', function() {
            const method = $(this).val();
            if (method) {
                $('.payment-card[data-method="' + method + '"]').addClass('selected').siblings().removeClass('selected');
                updatePaymentMethodInfo(method);
                toggleReferenceField(method);
            }
        });
        
        // Sélectionner la méthode par défaut
        const checkedMethod = $('input[name="payment_method"]:checked').val() || 'cash';
        $('.payment-card[data-method="' + checkedMethod + '"]').click();
        
        // Validation du montant de paiement
        const amountInput = document.getElementById('amount');
        const maxAmount = {{ $remainingAmount }};
        
        amountInput.addEventListener('input', function() {
            if (parseFloat(this.value) > maxAmount) {
                this.value = maxAmount;
            }
        });
        
        // Validation du formulaire avant soumission
        $('#paymentForm').on('submit', function(e) {
            e.preventDefault();

            const amount = parseFloat($('#amount').val());
            const methodSelected = $('input[name="payment_method"]:checked').length > 0;
            const selectedMethod = $('input[name="payment_method"]:checked').val();

            // Validation du montant
            if (amount <= 0) {
                showError('Le montant doit être supérieur à 0');
                return false;
            }

            if (amount > maxAmount) {
                showError('Le montant ne peut pas dépasser ' + maxAmount + ' FCFA');
                return false;
            }

            // Validation de la méthode de paiement
            if (!methodSelected) {
                showError('Veuillez sélectionner une méthode de paiement');
                return false;
            }

            // Validation du numéro de référence pour les méthodes qui le requièrent
            if (selectedMethod !== 'cash') {
                const referenceNumber = $('#reference_number').val().trim();
                if (!referenceNumber) {
                    showError('Le numéro de référence est requis pour cette méthode de paiement');
                    $('#reference_number').focus();
                    return false;
                }
            }

            // Afficher une confirmation avant soumission
            Swal.fire({
                title: 'Confirmer le paiement',
                html: `
                    <div class="text-start">
                        <p><strong>Montant:</strong> ${amount.toLocaleString()} FCFA</p>
                        <p><strong>Méthode:</strong> ${getPaymentMethodLabel(selectedMethod)}</p>
                        ${selectedMethod !== 'cash' ? '<p><strong>Référence:</strong> ' + $('#reference_number').val() + '</p>' : ''}
                    </div>
                `,
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'Oui, enregistrer',
                cancelButtonText: 'Annuler',
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Soumettre le formulaire
                    this.submit();
                }
            });
        });
    });

    // Fonction pour afficher/masquer le champ de référence
    function toggleReferenceField(method) {
        const referenceField = $('.reference-field');
        const referenceInput = $('#reference_number');

        if (method === 'cash') {
            referenceField.slideUp(300);
            referenceInput.removeAttr('required').val('');
        } else {
            referenceField.slideDown(300);
            referenceInput.attr('required', 'required');
        }
    }

    // Fonction pour mettre à jour les informations de la méthode de paiement
    function updatePaymentMethodInfo(method) {
        const infoBox = $('#paymentMethodInfo');
        const infoTitle = infoBox.find('h6 span');
        const infoText = infoBox.find('p');

        const methodInfo = {
            'cash': {
                title: 'Paiement en espèces',
                text: 'Assurez-vous de compter correctement les espèces et de délivrer un reçu au client.',
                icon: 'fas fa-money-bill-wave',
                class: 'alert-success'
            },
            'bank_transfer': {
                title: 'Virement bancaire',
                text: 'Vérifiez le numéro de référence de la transaction bancaire avant de valider.',
                icon: 'fas fa-university',
                class: 'alert-primary'
            },
            'check': {
                title: 'Paiement par chèque',
                text: 'Vérifiez la validité du chèque et notez le numéro de référence.',
                icon: 'fas fa-money-check-alt',
                class: 'alert-info'
            },
            'mobile_money': {
                title: 'Mobile Money',
                text: 'Confirmez la réception du paiement mobile avant de valider la transaction.',
                icon: 'fas fa-mobile-alt',
                class: 'alert-warning'
            }
        };

        const info = methodInfo[method];
        if (info) {
            infoBox.removeClass('alert-info alert-success alert-primary alert-warning').addClass(info.class);
            infoBox.find('i').removeClass().addClass(info.icon + ' me-2');
            infoTitle.text(info.title);
            infoText.text(info.text);
        }
    }

    // Fonction pour obtenir le libellé de la méthode de paiement
    function getPaymentMethodLabel(method) {
        const labels = {
            'cash': 'Espèces',
            'bank_transfer': 'Virement bancaire',
            'check': 'Chèque',
            'mobile_money': 'Mobile Money'
        };
        return labels[method] || method;
    }

    // Fonction pour afficher les erreurs
    function showError(message) {
        Swal.fire({
            title: 'Erreur',
            text: message,
            icon: 'error',
            confirmButtonText: 'OK',
            confirmButtonColor: '#dc3545'
        });
    }

    // Fonction de débogage
    function debugPaymentSelection() {
        if (typeof PaymentMethodFix !== 'undefined') {
            PaymentMethodFix.debug();
        } else {
            console.log('🔍 Debug manuel:');
            console.log('Méthode sélectionnée:', $('input[name="payment_method"]:checked').val());
            console.log('Cartes avec classe selected:', $('.payment-card.selected').length);
            console.log('Radio buttons cochés:', $('input[name="payment_method"]:checked').length);
        }
    }

    // Fonctions pour l'édition des reçus
    function toggleReceiptEditor() {
        // Basculer l'affichage des boutons d'édition
        $('.btn-group').toggle();
    }

    function viewReceipt(paymentId) {
        // Ouvrir la vue du reçu dans une nouvelle fenêtre
        window.open(`/accountant/payments/${paymentId}/receipt`, '_blank');
    }

    function editPayment(paymentId) {
        // Charger les données du paiement et ouvrir le modal d'édition
        fetch(`/accountant/payments/${paymentId}`)
            .then(response => response.json())
            .then(payment => {
                $('#edit_payment_id').val(payment.id);
                $('#edit_amount').val(payment.amount);
                $('#edit_payment_date').val(payment.payment_date);
                $('#edit_payment_method').val(payment.payment_method);
                $('#edit_reference_number').val(payment.reference_number || '');
                $('#edit_notes').val(payment.notes || '');

                $('#receiptEditModal').modal('show');
            })
            .catch(error => {
                console.error('Erreur lors du chargement du paiement:', error);
                showError('Impossible de charger les données du paiement');
            });
    }

    function savePaymentEdit() {
        const formData = new FormData(document.getElementById('receiptEditForm'));
        const paymentId = $('#edit_payment_id').val();

        fetch(`/accountant/payments/${paymentId}`, {
            method: 'PUT',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                $('#receiptEditModal').modal('hide');
                location.reload(); // Recharger la page pour voir les modifications
            } else {
                showError(data.message || 'Erreur lors de la sauvegarde');
            }
        })
        .catch(error => {
            console.error('Erreur lors de la sauvegarde:', error);
            showError('Erreur lors de la sauvegarde du paiement');
        });
    }

    function printReceipt(paymentId) {
        // Ouvrir la vue d'impression du reçu
        window.open(`/accountant/payments/${paymentId}/receipt?print=1`, '_blank');
    }
</script>
@endpush
