<?php $__env->startSection('title', 'Détail du Recouvrement'); ?>

<?php $__env->startPush('styles'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/payments-table-responsive.css')); ?>">
<style>
    /* Style général de la page */
    .recovery-detail {
        background-color: #f8f9fa;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
        padding: 20px;
        margin-bottom: 30px;
    }
    
    /* En-tête avec informations principales */
    .recovery-header {
        background: linear-gradient(to right bottom, #1E88E5, #0D47A1);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }
    
    .recovery-header h2 {
        margin-bottom: 15px;
        font-weight: 600;
    }
    
    .recovery-header .badge {
        font-size: 0.8rem;
        padding: 5px 10px;
        border-radius: 30px;
        margin-left: 10px;
    }
    
    /* Cartes d'information */
    .info-card {
        background-color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        height: 100%;
        transition: all 0.3s ease;
    }
    
    .info-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
    }
    
    .info-card h5 {
        color: #1E88E5;
        font-weight: 600;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
    }
    
    .info-card h5 i {
        margin-right: 10px;
    }
    
    .info-card .detail-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        padding-bottom: 10px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .info-card .detail-item:last-child {
        border-bottom: none;
        padding-bottom: 0;
        margin-bottom: 0;
    }
    
    .info-card .detail-label {
        color: #6c757d;
        font-weight: 500;
    }
    
    .info-card .detail-value {
        font-weight: 600;
        text-align: right;
    }
    
    /* Barre de progression */
    .payment-progress {
        height: 10px;
        border-radius: 5px;
        margin: 15px 0;
    }
    
    /* Badges et étiquettes */
    .badge-paid {
        background-color: rgba(25, 135, 84, 0.1);
        color: #198754;
    }
    
    .badge-partial {
        background-color: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }
    
    .badge-unpaid {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }
    
    /* Boutons d'action */
    .action-buttons .btn {
        padding: 10px 20px;
        border-radius: 5px;
        font-weight: 500;
        margin-right: 10px;
        display: inline-flex;
        align-items: center;
    }
    
    .action-buttons .btn i {
        margin-right: 8px;
    }
    
    /* Tableau des paiements */
    .payments-table {
        background-color: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .payments-table table {
        min-width: 1200px; /* Largeur minimale pour afficher toutes les colonnes */
        width: 100%;
    }

    .payments-table thead {
        background-color: #1E88E5;
        color: white;
    }

    .payments-table th {
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.5px;
        padding: 12px 15px;
        white-space: nowrap; /* Empêche le retour à la ligne dans les en-têtes */
        min-width: 100px; /* Largeur minimale pour chaque colonne */
    }

    .payments-table td {
        padding: 12px 15px;
        vertical-align: middle;
        white-space: nowrap; /* Empêche le retour à la ligne dans les cellules */
    }

    /* Largeurs spécifiques pour certaines colonnes importantes */
    .payments-table th:nth-child(1), .payments-table td:nth-child(1) { min-width: 120px; } /* Date */
    .payments-table th:nth-child(2), .payments-table td:nth-child(2) { min-width: 140px; } /* Réf. Paiement */
    .payments-table th:nth-child(3), .payments-table td:nth-child(3) { min-width: 120px; } /* Montant */
    .payments-table th:nth-child(4), .payments-table td:nth-child(4) { min-width: 110px; } /* Méthode */
    .payments-table th:nth-child(5), .payments-table td:nth-child(5) { min-width: 130px; } /* Réf. Transaction */
    .payments-table th:nth-child(6), .payments-table td:nth-child(6) { min-width: 100px; } /* Agent */
    .payments-table th:nth-child(7), .payments-table td:nth-child(7) { min-width: 100px; } /* Poste */
    .payments-table th:nth-child(8), .payments-table td:nth-child(8) { min-width: 110px; } /* Statut */
    .payments-table th:nth-child(9), .payments-table td:nth-child(9) { min-width: 150px; } /* Commentaire */
    .payments-table th:nth-child(10), .payments-table td:nth-child(10) { min-width: 100px; } /* Actions */

    /* Responsive */
    @media (max-width: 768px) {
        .recovery-header {
            padding: 15px;
        }

        .info-card {
            margin-bottom: 15px;
        }

        .action-buttons .btn {
            margin-bottom: 10px;
            width: 100%;
        }

        .payments-table {
            margin: 0 -15px; /* Étendre le tableau sur toute la largeur sur mobile */
        }

        .payments-table table {
            min-width: 1200px; /* Maintenir la largeur minimale même sur mobile */
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid mt-4">
    <!-- Bouton de retour -->
    <div class="mb-3">
        <a href="<?php echo e(route('accountant.recoveries.index')); ?>" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-2"></i> Retour à la liste
        </a>
    </div>
    
    <!-- En-tête avec informations principales -->
    <div class="recovery-header">
        <div class="d-flex justify-content-between align-items-center flex-wrap">
            <div>
                <h2>
                    Facture #<?php echo e($sale->invoice_number); ?>

                    <?php if($paymentStatus == 'paid'): ?>
                        <span class="badge bg-success">Payé</span>
                    <?php elseif($paymentStatus == 'partial'): ?>
                        <span class="badge bg-warning">Paiement Partiel</span>
                    <?php else: ?>
                        <span class="badge bg-danger">Impayé</span>
                    <?php endif; ?>
                </h2>
                <p class="mb-0">Date de création: <?php echo e($sale->created_at->format('d/m/Y à H:i')); ?></p>
            </div>
            <div class="action-buttons">
                <?php if($sale->admin_validation_status !== 'rejected'): ?>
                <a href="<?php echo e(route('accountant.recoveries.edit', $sale->id)); ?>" class="btn btn-success">
                    <i class="fas fa-money-bill-wave"></i> Enregistrer un paiement
                </a>
                <?php endif; ?>
                <a href="tel:<?php echo e($sale->customer_phone); ?>" class="btn btn-info text-white">
                    <i class="fas fa-phone"></i> Contacter le client
                </a>
                <?php if($sale->admin_validation_status === 'rejected'): ?>
                <div class="alert alert-danger mt-2">
                    <i class="fas fa-ban"></i> Cette vente a été rejetée par l'administrateur et ne peut pas recevoir de paiement.
                    <?php if($sale->admin_note): ?>
                    <p class="mb-0 mt-1"><strong>Raison :</strong> <?php echo e($sale->admin_note); ?></p>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Informations client -->
        <div class="col-md-4 mb-4">
            <div class="info-card">
                <h5><i class="fas fa-user"></i> Informations Client</h5>
                <div class="detail-item">
                    <span class="detail-label">Nom</span>
                    <span class="detail-value"><?php echo e($sale->customer_name); ?></span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Téléphone</span>
                    <span class="detail-value"><?php echo e($sale->customer_phone); ?></span>
                </div>
                <?php if($sale->customer_email): ?>
                <div class="detail-item">
                    <span class="detail-label">Email</span>
                    <span class="detail-value"><?php echo e($sale->customer_email); ?></span>
                </div>
                <?php endif; ?>
                <?php if($sale->customer_address): ?>
                <div class="detail-item">
                    <span class="detail-label">Adresse</span>
                    <span class="detail-value"><?php echo e($sale->customer_address); ?></span>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Détails de la facture -->
        <div class="col-md-4 mb-4">
            <div class="info-card">
                <h5><i class="fas fa-file-invoice"></i> Détails de la Facture</h5>
                <div class="detail-item">
                    <span class="detail-label">Numéro</span>
                    <span class="detail-value"><?php echo e($sale->invoice_number); ?></span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Date</span>
                    <span class="detail-value"><?php echo e($sale->created_at->format('d/m/Y')); ?></span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Âge</span>
                    <span class="detail-value"><?php echo e($sale->created_at->diffInDays(now())); ?> jours</span>
                </div>
                <?php if($sale->due_date): ?>
                <div class="detail-item">
                    <span class="detail-label">Échéance</span>
                    <span class="detail-value"><?php echo e($sale->due_date->format('d/m/Y')); ?></span>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Statut de paiement -->
        <div class="col-md-4 mb-4">
            <div class="info-card">
                <h5><i class="fas fa-chart-pie"></i> Statut de Paiement</h5>
                <div class="detail-item">
                    <span class="detail-label">Montant total</span>
                    <span class="detail-value"><?php echo e(number_format($totalAmount, 0, ',', ' ')); ?> FCFA</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Montant payé</span>
                    <span class="detail-value text-success"><?php echo e(number_format($paidAmount, 0, ',', ' ')); ?> FCFA</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Reste à payer</span>
                    <span class="detail-value <?php echo e($remainingAmount > 0 ? 'text-danger' : 'text-success'); ?>"><?php echo e(number_format($remainingAmount, 0, ',', ' ')); ?> FCFA</span>
                </div>
                
                <!-- Barre de progression -->
                <div class="mt-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span>Progression du paiement</span>
                        <span><?php echo e(round($paymentPercentage)); ?>%</span>
                    </div>
                    <div class="progress payment-progress">
                        <div class="progress-bar <?php echo e($paymentPercentage == 100 ? 'bg-success' : ($paymentPercentage > 0 ? 'bg-warning' : 'bg-danger')); ?>" 
                            role="progressbar" 
                            style="width: <?php echo e($paymentPercentage); ?>%" 
                            aria-valuenow="<?php echo e($paymentPercentage); ?>" 
                            aria-valuemin="0" 
                            aria-valuemax="100">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Historique des paiements -->
    <div class="row">
        <div class="col-12">
            <div class="info-card">
                <h5><i class="fas fa-history"></i> Historique des Paiements</h5>

                <?php if($sale->payments && $sale->payments->count() > 0): ?>
                <!-- Indicateur de défilement horizontal -->
                <div class="scroll-indicator d-lg-none">
                    <i class="fas fa-arrows-alt-h"></i>
                    <span>Faites glisser horizontalement pour voir toutes les colonnes</span>
                </div>

                <div class="payments-table-container">
                    <div class="table-responsive payments-table">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Réf. Paiement</th>
                                <th>Montant</th>
                                <th>Méthode</th>
                                <th>Réf. Transaction</th>
                                <th>Agent</th>
                                <th>Poste</th>
                                <th>Statut</th>
                                <th>Commentaire</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $sale->payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($payment->created_at->format('d/m/Y H:i')); ?></td>
                                <td><span class="badge bg-primary"><?php echo e($payment->reference ?? 'N/A'); ?></span></td>
                                <td><?php echo e(number_format($payment->amount, 0, ',', ' ')); ?> FCFA</td>
                                <td>
                                    <?php switch($payment->payment_method):
                                        case ('cash'): ?>
                                            <span class="badge bg-success"><i class="fas fa-money-bill-wave me-1"></i>Espèces</span>
                                            <?php break; ?>
                                        <?php case ('bank_transfer'): ?>
                                            <span class="badge bg-primary"><i class="fas fa-university me-1"></i>Virement</span>
                                            <?php break; ?>
                                        <?php case ('check'): ?>
                                            <span class="badge bg-info"><i class="fas fa-money-check-alt me-1"></i>Chèque</span>
                                            <?php break; ?>
                                        <?php case ('mobile_money'): ?>
                                            <span class="badge bg-warning"><i class="fas fa-mobile-alt me-1"></i>Mobile Money</span>
                                            <?php break; ?>
                                        <?php default: ?>
                                            <span class="badge bg-secondary"><?php echo e($payment->payment_method); ?></span>
                                    <?php endswitch; ?>
                                </td>
                                <td><?php echo e($payment->reference_number ?? '-'); ?></td>
                                <td><?php echo e($payment->cashier->name ?? 'N/A'); ?></td>
                                <td><?php echo e($payment->position ?? 'N/A'); ?></td>
                                <td>
                                    <?php if($payment->status == 'completed'): ?>
                                        <span class="badge badge-paid"><i class="fas fa-check-circle me-1"></i>Complété</span>
                                    <?php elseif($payment->status == 'pending'): ?>
                                        <span class="badge badge-partial"><i class="fas fa-clock me-1"></i>En attente</span>
                                    <?php else: ?>
                                        <span class="badge badge-unpaid"><i class="fas fa-times-circle me-1"></i>Échoué</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($payment->notes ?? '-'); ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <?php if($payment->position === 'Caissier' || $payment->position === 'cashier'): ?>
                                            <!-- Reçu du côté caissier -->
                                            <a href="<?php echo e(route('accountant.cashier-receipt', $payment->id)); ?>"
                                               target="_blank"
                                               class="btn btn-sm btn-outline-primary"
                                               title="Voir le reçu caissier">
                                                <i class="fas fa-receipt"></i>
                                            </a>
                                        <?php else: ?>
                                            <!-- Reçu du côté comptable -->
                                            <a href="<?php echo e(route('accountant.payments.receipt', $payment->id)); ?>"
                                               target="_blank"
                                               class="btn btn-sm btn-outline-primary"
                                               title="Voir le reçu comptable">
                                                <i class="fas fa-receipt"></i>
                                            </a>
                                        <?php endif; ?>

                                        <button type="button"
                                                class="btn btn-sm btn-outline-info"
                                                onclick="showPaymentDetails(<?php echo e($payment->id); ?>)"
                                                title="Détails du paiement">
                                            <i class="fas fa-info-circle"></i>
                                        </button>

                                        <?php if($payment->status == 'completed'): ?>
                                        <button type="button"
                                                class="btn btn-sm btn-outline-success"
                                                onclick="printReceipt(<?php echo e($payment->id); ?>)"
                                                title="Imprimer le reçu">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                    </div>
                </div>
                <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-circle fa-3x text-muted mb-3"></i>
                    <p class="mb-0">Aucun paiement enregistré pour cette vente.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Produits de la vente -->
    <?php if(isset($sale->items) && count($sale->items) > 0): ?>
    <div class="row mt-4">
        <div class="col-12">
            <div class="info-card">
                <h5><i class="fas fa-shopping-cart"></i> Produits</h5>
                
                <div class="table-responsive payments-table">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Produit</th>
                                <th>Quantité</th>
                                <th>Prix unitaire</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $sale->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($item->product_name); ?></td>
                                <td><?php echo e($item->quantity); ?></td>
                                <td><?php echo e(number_format($item->unit_price, 0, ',', ' ')); ?> FCFA</td>
                                <td><?php echo e(number_format($item->quantity * $item->unit_price, 0, ',', ' ')); ?> FCFA</td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Modal pour les détails du paiement -->
<div class="modal fade" id="paymentDetailsModal" tabindex="-1" aria-labelledby="paymentDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="paymentDetailsModalLabel">
                    <i class="fas fa-info-circle me-2"></i>Détails du paiement
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="paymentDetailsContent">
                <!-- Le contenu sera chargé dynamiquement -->
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<?php if(config('app.debug')): ?>
<script src="<?php echo e(asset('js/payments-table-diagnostic.js')); ?>"></script>
<script src="<?php echo e(asset('js/receipt-access-test.js')); ?>"></script>
<?php endif; ?>
<script>
// Amélioration de l'expérience utilisateur pour le tableau des paiements
document.addEventListener('DOMContentLoaded', function() {
    const paymentsTable = document.querySelector('.payments-table .table-responsive');
    const scrollIndicator = document.querySelector('.scroll-indicator');

    if (paymentsTable && scrollIndicator) {
        // Animer l'indicateur au chargement de la page
        setTimeout(() => {
            scrollIndicator.classList.add('animate');
        }, 1000);

        // Masquer l'indicateur après le premier défilement
        paymentsTable.addEventListener('scroll', function() {
            if (this.scrollLeft > 10) {
                scrollIndicator.style.opacity = '0.5';
                scrollIndicator.classList.remove('animate');
            } else {
                scrollIndicator.style.opacity = '1';
            }
        });

        // Indicateur de progression du défilement
        paymentsTable.addEventListener('scroll', function() {
            const scrollLeft = this.scrollLeft;
            const scrollWidth = this.scrollWidth - this.clientWidth;
            const progress = (scrollLeft / scrollWidth) * 100;

            document.documentElement.style.setProperty('--scroll-progress', progress + '%');
        });
    }
});
</script>
<script>
    // Initialiser les tooltips Bootstrap
    document.addEventListener('DOMContentLoaded', function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });

    // Fonction pour afficher les détails d'un paiement
    function showPaymentDetails(paymentId) {
        const modal = new bootstrap.Modal(document.getElementById('paymentDetailsModal'));
        const content = document.getElementById('paymentDetailsContent');

        // Afficher le spinner de chargement
        content.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                </div>
            </div>
        `;

        modal.show();

        // Charger les détails du paiement
        fetch(`/accountant/payments/${paymentId}`)
            .then(response => response.json())
            .then(data => {
                content.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">Informations générales</h6>
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td><strong>Référence:</strong></td>
                                    <td>${data.reference || 'N/A'}</td>
                                </tr>
                                <tr>
                                    <td><strong>Montant:</strong></td>
                                    <td class="text-success"><strong>${new Intl.NumberFormat('fr-FR').format(data.amount)} FCFA</strong></td>
                                </tr>
                                <tr>
                                    <td><strong>Date de paiement:</strong></td>
                                    <td>${new Date(data.payment_date).toLocaleDateString('fr-FR')}</td>
                                </tr>
                                <tr>
                                    <td><strong>Statut:</strong></td>
                                    <td>
                                        ${data.status === 'completed' ?
                                            '<span class="badge bg-success">Complété</span>' :
                                            data.status === 'pending' ?
                                            '<span class="badge bg-warning">En attente</span>' :
                                            '<span class="badge bg-danger">Échoué</span>'
                                        }
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">Détails de transaction</h6>
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td><strong>Méthode:</strong></td>
                                    <td>${getPaymentMethodLabel(data.payment_method)}</td>
                                </tr>
                                <tr>
                                    <td><strong>Réf. transaction:</strong></td>
                                    <td>${data.reference_number || '-'}</td>
                                </tr>
                                <tr>
                                    <td><strong>Agent:</strong></td>
                                    <td>${data.cashier_name || 'N/A'}</td>
                                </tr>
                                <tr>
                                    <td><strong>Poste:</strong></td>
                                    <td>${data.position || 'N/A'}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    ${data.notes ? `
                        <div class="mt-3">
                            <h6 class="text-primary">Notes</h6>
                            <div class="alert alert-info">
                                <i class="fas fa-sticky-note me-2"></i>${data.notes}
                            </div>
                        </div>
                    ` : ''}
                `;
            })
            .catch(error => {
                console.error('Erreur lors du chargement des détails:', error);
                content.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Erreur lors du chargement des détails du paiement.
                    </div>
                `;
            });
    }

    // Fonction pour imprimer un reçu
    function printReceipt(paymentId) {
        // Déterminer l'URL du reçu en fonction du poste de l'agent
        const paymentRow = document.querySelector(`button[onclick="printReceipt(${paymentId})"]`).closest('tr');
        const position = paymentRow.cells[6].textContent.trim(); // Colonne "Poste"

        let receiptUrl;
        if (position === 'Caissier' || position === 'cashier') {
            receiptUrl = `/accountant/cashier-receipt/${paymentId}?print=1`;
        } else {
            receiptUrl = `/accountant/payments/${paymentId}/receipt?print=1`;
        }

        // Ouvrir la fenêtre d'impression
        window.open(receiptUrl, '_blank');
    }

    // Fonction pour obtenir le libellé de la méthode de paiement
    function getPaymentMethodLabel(method) {
        const labels = {
            'cash': '<span class="badge bg-success"><i class="fas fa-money-bill-wave me-1"></i>Espèces</span>',
            'bank_transfer': '<span class="badge bg-primary"><i class="fas fa-university me-1"></i>Virement bancaire</span>',
            'check': '<span class="badge bg-info"><i class="fas fa-money-check-alt me-1"></i>Chèque</span>',
            'mobile_money': '<span class="badge bg-warning"><i class="fas fa-mobile-alt me-1"></i>Mobile Money</span>'
        };
        return labels[method] || `<span class="badge bg-secondary">${method}</span>`;
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.accountant', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/accountant/recoveries/show.blade.php ENDPATH**/ ?>