{{-- LOG: <PERSON><PERSON><PERSON> process.blade.php chargé --}}
@extends('layouts.cashier')

@section('title', 'Règlement de vente')

@section('styles')
<style>
    /* Variables */
    :root {
        --primary-color: #2196F3;
        --primary-light: #BBDEFB;
        --primary-dark: #1976D2;
        --success-color: #4CAF50;
        --success-light: #E8F5E9;
        --warning-color: #FF9800;
        --warning-light: #FFF3E0;
        --danger-color: #F44336;
        --danger-light: #FFEBEE;
        --info-color: #00BCD4;
        --info-light: #E0F7FA;
        --dark-color: #101828;
        --text-color: #344054;
        --border-color: #EAECF0;
        --background-color: #F9FAFB;
        --card-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    }
    
    /* Styles généraux */
    body {
        background-color: var(--background-color);
        color: var(--text-color);
    }
    
    .container-fluid {
        padding: 2rem 2.5rem;
        max-width: 1600px;
        margin: 0 auto;
        animation: fadeIn 0.5s ease-out;
    }
    
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--border-color);
    }
    
    .page-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--dark-color);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin: 0;
    }
    
    .page-title i {
        color: var(--primary-color);
    }
    
    /* Cartes de paiement */
    .payment-card {
        transition: all 0.3s ease;
        cursor: pointer;
        border: 1px solid var(--border-color);
        border-radius: 12px;
        background-color: white;
        box-shadow: 0 1px 3px rgba(16, 24, 40, 0.1);
        height: 100%;
    }
    
    .payment-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--card-shadow);
    }
    
    .payment-card.selected {
        border-color: var(--primary-color);
        background-color: var(--primary-light);
        box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.2);
    }
    
    .payment-method-icon {
        font-size: 2.5rem;
        margin-bottom: 0.75rem;
        transition: transform 0.2s ease;
    }
    
    .payment-card:hover .payment-method-icon {
        transform: scale(1.1);
    }
    
    /* Éléments d'échéancier */
    .schedule-item {
        border-left: 4px solid #ccc;
        padding: 1rem 1.25rem;
        margin-bottom: 1rem;
        transition: all 0.2s ease;
        background-color: white;
        border-radius: 0 8px 8px 0;
        box-shadow: 0 1px 3px rgba(16, 24, 40, 0.1);
    }
    
    .schedule-item:hover {
        border-left-color: var(--primary-color);
        box-shadow: 0 4px 8px rgba(16, 24, 40, 0.1);
    }
    
    .schedule-item.overdue {
        border-left-color: var(--danger-color);
        background-color: var(--danger-light);
    }
    
    .schedule-item.paid {
        border-left-color: var(--success-color);
        background-color: var(--success-light);
    }
    
    /* Résumé de paiement */
    .payment-summary {
        background-color: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: var(--card-shadow);
        border: 1px solid var(--border-color);
    }
    
    /* Cartes */
    .card {
        border-radius: 12px;
        border: 1px solid var(--border-color);
        box-shadow: var(--card-shadow);
        overflow: hidden;
        margin-bottom: 1.5rem;
        background-color: white;
    }
    
    .card-header {
        padding: 1rem 1.5rem;
        background-color: #FCFCFD;
        border-bottom: 1px solid var(--border-color);
        font-weight: 600;
        color: var(--dark-color);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .card-header i {
        color: var(--primary-color);
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    /* Formulaires */
    .form-label {
        font-weight: 500;
        color: var(--dark-color);
        margin-bottom: 0.5rem;
    }
    
    .form-control {
        border-radius: 8px;
        border: 1px solid var(--border-color);
        padding: 0.625rem 1rem;
        font-size: 0.875rem;
        transition: all 0.2s ease;
    }
    
    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.1);
    }
    
    /* Boutons */
    .btn {
        border-radius: 8px;
        padding: 0.625rem 1.25rem;
        font-weight: 500;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }
    
    .btn-primary:hover {
        background-color: var(--primary-dark);
        border-color: var(--primary-dark);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(33, 150, 243, 0.2);
    }
    
    .btn-outline-secondary {
        border-color: var(--border-color);
        color: var(--text-color);
    }
    
    .btn-outline-secondary:hover {
        background-color: #F9FAFB;
        color: var(--dark-color);
    }
    
    /* Alertes */
    .alert {
        border-radius: 12px;
        border: 1px solid transparent;
        padding: 1rem 1.25rem;
    }
    
    .alert-info {
        background-color: var(--info-light);
        border-color: var(--info-color);
        color: var(--info-color);
    }
    
    /* Animations */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .animate-fade-in {
        animation: fadeIn 0.5s ease-out forwards;
    }
    
    .payment-progress {
        height: 10px;
        border-radius: 5px;
        margin: 1rem 0;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- En-tête de page avec titre -->
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-cash-register"></i>
            <span>Règlement de vente</span>
        </h1>
        <div class="breadcrumb-navigation">
            <a href="{{ route('cashier.dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-home"></i> Tableau de bord
            </a>
            <a href="{{ route('cashier.payments.pending') }}" class="btn btn-outline-secondary">
                <i class="fas fa-clock"></i> Ventes en attente
            </a>
        </div>
    </div>

    @include('partials.alerts')

    <div class="row">
        <!-- Informations sur la vente -->
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-info-circle me-1"></i>
                    Détails de la vente
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h5 class="d-flex align-items-center mb-3" style="color: var(--primary-color);">
                            <i class="fas fa-user-circle me-2"></i> Client
                        </h5>
                        <div class="info-item d-flex justify-content-between mb-2">
                            <span class="text-muted">Nom:</span>
                            <span class="fw-medium">{{ $sale->customer_name }}</span>
                        </div>
                        @if($sale->customer_phone)
                            <div class="info-item d-flex justify-content-between mb-2">
                                <span class="text-muted">Téléphone:</span>
                                <a href="tel:{{ $sale->customer_phone }}" class="text-decoration-none fw-medium">{{ $sale->customer_phone }}</a>
                            </div>
                        @endif
                    </div>
                    
                    <div class="mb-4">
                        <h5 class="d-flex align-items-center mb-3" style="color: var(--primary-color);">
                            <i class="fas fa-box me-2"></i> Produit
                        </h5>
                        <div class="info-item d-flex justify-content-between mb-2">
                            <span class="text-muted">Nom:</span>
                            <span class="fw-medium">{{ $sale->product_name }}</span>
                        </div>
                        <div class="info-item d-flex justify-content-between mb-2">
                            <span class="text-muted">Quantité:</span>
                            <span class="fw-medium">{{ number_format($sale->quantity, 0, ',', ' ') }} {{ $sale->unit }}</span>
                        </div>
                        <div class="info-item d-flex justify-content-between mb-2">
                            <span class="text-muted">Prix unitaire:</span>
                            <span class="fw-medium">{{ number_format($sale->unit_price, 0, ',', ' ') }} FCFA</span>
                        </div>
                    </div>
                    
                    <div class="payment-summary">
                        <h5 class="d-flex align-items-center mb-3" style="color: var(--primary-color);">
                            <i class="fas fa-file-invoice-dollar me-2"></i> Résumé financier
                        </h5>
                        <div class="info-item d-flex justify-content-between mb-3">
                            <span class="text-muted">Montant total:</span>
                            <span class="fw-bold" style="font-size: 1.1rem;">{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</span>
                        </div>
                        <div class="info-item d-flex justify-content-between mb-3">
                            <span class="text-muted">Déjà payé:</span>
                            <span class="fw-medium text-success">{{ number_format($sale->amount_paid, 0, ',', ' ') }} FCFA</span>
                        </div>
                        <div class="info-item d-flex justify-content-between mb-3" style="padding-top: 0.75rem; border-top: 1px solid var(--border-color);">
                            <span class="fw-medium">Reste à payer:</span>
                            <span class="fw-bold text-danger" style="font-size: 1.2rem;">{{ number_format($sale->total_amount - $sale->amount_paid, 0, ',', ' ') }} FCFA</span>
                        </div>
                        
                        <div class="mt-4">
                            <label class="form-label d-flex align-items-center">
                                <i class="fas fa-chart-pie text-primary me-2"></i>
                                Progression du paiement
                            </label>
                            <div class="progress" style="height: 10px; border-radius: 10px; background-color: #F2F4F7;">
                                @php
                                    $percentage = ($sale->amount_paid / $sale->total_amount) * 100;
                                @endphp
                                <div class="progress-bar" role="progressbar" 
                                    style="width: {{ $percentage }}%; background: linear-gradient(90deg, #4CAF50, #8BC34A); border-radius: 10px;" 
                                    aria-valuenow="{{ $percentage }}" aria-valuemin="0" aria-valuemax="100">
                                    {{ round($percentage) }}%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Échéanciers de paiement -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-calendar-alt me-1"></i>
                        Échéanciers de paiement
                    </div>
                    <a href="{{ route('cashier.payments.schedule.create', $sale->id) }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-plus-circle"></i> Créer
                    </a>
                </div>
                <div class="card-body">
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                        <p class="mb-0">Aucun échéancier défini</p>
                        <small class="text-muted">Créez un échéancier pour faciliter le suivi des paiements</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Formulaire de paiement -->
        <div class="col-md-8">
            <div class="card mb-4 animate-fade-in" style="animation-delay: 0.2s;">
                <div class="card-header">
                    <i class="fas fa-cash-register"></i>
                    Enregistrer un paiement
                </div>
                <div class="card-body">
                    <form id="paymentForm" action="{{ route('cashier.payments.store', $sale->id) }}" method="POST">
                        @csrf
                        
                        <input type="hidden" name="payment_schedule_id" id="payment_schedule_id">
                        
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label for="amount" class="form-label d-flex align-items-center">
                                        <i class="fas fa-coins text-primary me-2"></i>
                                        Montant à encaisser <span class="text-danger ms-1">*</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="amount" name="amount" 
                                            step="0.01" min="1" max="{{ $sale->total_amount - $sale->amount_paid }}" 
                                            value="{{ old('amount', $sale->total_amount - $sale->amount_paid) }}" required>
                                        <span class="input-group-text">FCFA</span>
                                    </div>
                                    <small class="form-text text-muted mt-1">Montant maximum: {{ number_format($sale->total_amount - $sale->amount_paid, 0, ',', ' ') }} FCFA</small>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="payment_date" class="form-label d-flex align-items-center">
                                        <i class="fas fa-calendar-alt text-primary me-2"></i>
                                        Date du paiement <span class="text-danger ms-1">*</span>
                                    </label>
                                    <input type="date" class="form-control" id="payment_date" name="payment_date" 
                                        value="{{ old('payment_date', date('Y-m-d')) }}" required>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="reference_number" class="form-label d-flex align-items-center">
                                        <i class="fas fa-hashtag text-primary me-2"></i>
                                        Numéro de référence
                                    </label>
                                    <input type="text" class="form-control" id="reference_number" name="reference_number" 
                                        value="{{ old('reference_number') }}" placeholder="Ex: N° chèque, référence virement...">
                                    <small class="form-text text-muted mt-1">Obligatoire pour les paiements par chèque ou virement</small>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="notes" class="form-label d-flex align-items-center">
                                        <i class="fas fa-sticky-note text-primary me-2"></i>
                                        Notes
                                    </label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3" 
                                        placeholder="Informations complémentaires sur le paiement...">{{ old('notes') }}</textarea>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <label class="form-label d-flex align-items-center mb-3">
                                    <i class="fas fa-credit-card text-primary me-2"></i>
                                    Mode de paiement <span class="text-danger ms-1">*</span>
                                </label>
                                
                                <div class="row g-3">
                                    <div class="col-6">
                                        <div class="payment-card text-center p-3 rounded selected" data-method="cash">
                                            <i class="fas fa-money-bill-wave payment-method-icon text-success"></i>
                                            <h6 class="mb-0 fw-medium">Espèces</h6>
                                            <input type="radio" name="payment_method" value="cash" class="d-none" checked>
                                        </div>
                                    </div>
                                    
                                    <div class="col-6">
                                        <div class="payment-card text-center p-3 rounded" data-method="bank_transfer">
                                            <i class="fas fa-university payment-method-icon text-primary"></i>
                                            <h6 class="mb-0 fw-medium">Virement</h6>
                                            <input type="radio" name="payment_method" value="bank_transfer" class="d-none">
                                        </div>
                                    </div>
                                    
                                    <div class="col-6">
                                        <div class="payment-card text-center p-3 rounded" data-method="check">
                                            <i class="fas fa-money-check-alt payment-method-icon text-info"></i>
                                            <h6 class="mb-0 fw-medium">Chèque</h6>
                                            <input type="radio" name="payment_method" value="check" class="d-none">
                                        </div>
                                    </div>
                                    
                                    <div class="col-6">
                                        <div class="payment-card text-center p-3 rounded" data-method="mobile_money">
                                            <i class="fas fa-mobile-alt payment-method-icon text-warning"></i>
                                            <h6 class="mb-0 fw-medium">Mobile Money</h6>
                                            <input type="radio" name="payment_method" value="mobile_money" class="d-none">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="alert alert-info mt-4" id="paymentMethodInfo" style="border-radius: 12px; border-left: 4px solid var(--info-color);">
                                    <h6 class="alert-heading d-flex align-items-center">
                                        <i class="fas fa-info-circle me-2"></i> 
                                        <span>Paiement en espèces</span>
                                    </h6>
                                    <p class="mb-0">Assurez-vous de compter correctement les espèces et de délivrer un reçu au client.</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4 pt-3" style="border-top: 1px solid var(--border-color);">
                            <a href="{{ route('cashier.payments.pending') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i> Retour
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> Enregistrer le paiement
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Historique des paiements -->
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-history me-1"></i>
                    Historique des paiements
                </div>
                <div class="card-body">
                    @if($sale->payments->isEmpty())
                        <div class="text-center py-4">
                            <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                            <p class="mb-0">Aucun paiement enregistré</p>
                            <small class="text-muted">Les paiements effectués apparaîtront ici</small>
                        </div>
                    @else
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Réf. Paiement</th>
                                        <th>Montant</th>
                                        <th>Mode</th>
                                        <th>Réf. Transaction</th>
                                        <th>Caissier</th>
                                        <th>Poste</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($sale->payments->sortByDesc('created_at') as $payment)
                                        <tr>
                                            <td>{{ $payment->payment_date->format('d/m/Y H:i') }}</td>
                                            <td><span class="badge bg-primary">{{ $payment->reference ?? 'N/A' }}</span></td>
                                            <td class="fw-bold">{{ number_format($payment->amount, 0, ',', ' ') }} FCFA</td>
                                            <td>
                                                @switch($payment->payment_method)
                                                    @case('cash')
                                                        <span class="badge bg-success">Espèces</span>
                                                        @break
                                                    @case('bank_transfer')
                                                        <span class="badge bg-primary">Virement</span>
                                                        @break
                                                    @case('check')
                                                        <span class="badge bg-info">Chèque</span>
                                                        @break
                                                    @case('mobile_money')
                                                        <span class="badge bg-warning">Mobile Money</span>
                                                        @break
                                                    @default
                                                        <span class="badge bg-secondary">Autre</span>
                                                @endswitch
                                            </td>
                                            <td>{{ $payment->reference_number ?? 'N/A' }}</td>
                                            <td>{{ $payment->cashier->name ?? 'N/A' }}</td>
                                            <td>{{ $payment->position ?? 'N/A' }}</td>
                                            <td>
                                                <a href="{{ route('cashier.payments.receipt', $payment->id) }}" class="btn btn-sm btn-outline-primary" target="_blank">
                                                    <i class="fas fa-receipt"></i> Reçu
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Gestionnaire d'erreurs global pour éviter que les erreurs JavaScript cassent la fonctionnalité
    window.addEventListener('error', function(e) {
        console.warn('Erreur JavaScript interceptée:', e.error);
        // Ne pas empêcher le fonctionnement normal de la page
        return false;
    });

    document.addEventListener('DOMContentLoaded', function() {
        // Gestion des cartes de méthode de paiement
        const paymentCards = document.querySelectorAll('.payment-card');
        const paymentMethodInfo = document.getElementById('paymentMethodInfo');
        const referenceNumberField = document.getElementById('reference_number');
        
        paymentCards.forEach(card => {
            card.addEventListener('click', function() {
                try {
                    console.log('Clic sur carte de paiement:', this.dataset.method);

                    // Supprimer la classe selected de toutes les cartes
                    paymentCards.forEach(c => {
                        c.classList.remove('selected');
                        // Décocher tous les radio buttons
                        const radio = c.querySelector('input[type="radio"]');
                        if (radio) {
                            radio.checked = false;
                        }
                    });

                    // Ajouter la classe selected à la carte cliquée
                    this.classList.add('selected');

                    // Sélectionner le radio button correspondant
                    const radio = this.querySelector('input[type="radio"]');
                    if (radio) {
                        radio.checked = true;
                        console.log('Radio button sélectionné:', radio.value);

                        // Déclencher l'événement change pour s'assurer que la sélection est prise en compte
                        radio.dispatchEvent(new Event('change', { bubbles: true }));
                    }

                    // Mettre à jour l'info du mode de paiement
                    const method = this.dataset.method;
                    if (method) {
                        updatePaymentMethodInfo(method);

                        // Gérer l'affichage du champ de référence
                        toggleReferenceField(method);

                        console.log('Méthode de paiement sélectionnée:', method);
                    }
                } catch (error) {
                    console.error('Erreur lors de la sélection du mode de paiement:', error);
                }
            });
        });
        
        // Fonction pour gérer l'affichage du champ de référence
        function toggleReferenceField(method) {
            const referenceField = document.querySelector('.reference-field');
            const referenceInput = document.getElementById('reference_number');

            if (method === 'cash') {
                // Masquer le champ de référence pour les paiements en espèces
                if (referenceField) {
                    referenceField.style.display = 'none';
                }
                if (referenceInput) {
                    referenceInput.removeAttribute('required');
                    referenceInput.value = ''; // Vider le champ
                }
            } else {
                // Afficher le champ de référence pour les autres méthodes
                if (referenceField) {
                    referenceField.style.display = 'block';
                }
                if (referenceInput) {
                    referenceInput.setAttribute('required', 'required');
                }
            }
        }

        // Fonction pour mettre à jour les informations sur la méthode de paiement
        function updatePaymentMethodInfo(method) {
            let infoBox = $('#paymentMethodInfo');
            let infoContent = '';
            
            // Définir la couleur de la bordure gauche en fonction de la méthode
            let borderColor = 'var(--info-color)';
            let iconClass = 'fas fa-info-circle';
            
            switch(method) {
                case 'cash':
                    borderColor = 'var(--success-color)';
                    iconClass = 'fas fa-money-bill-wave';
                    infoContent = 'Assurez-vous de compter correctement les espèces et de délivrer un reçu au client.';
                    title = 'Paiement en espèces';
                    break;
                case 'bank_transfer':
                    borderColor = 'var(--primary-color)';
                    iconClass = 'fas fa-university';
                    infoContent = 'Veuillez saisir le numéro de référence du virement dans le champ prévu à cet effet.';
                    title = 'Paiement par virement';
                    break;
                case 'check':
                    borderColor = 'var(--info-color)';
                    iconClass = 'fas fa-money-check-alt';
                    infoContent = 'N\'oubliez pas de noter le numéro du chèque dans le champ de référence.';
                    title = 'Paiement par chèque';
                    break;
                case 'mobile_money':
                    borderColor = 'var(--warning-color)';
                    iconClass = 'fas fa-mobile-alt';
                    infoContent = 'Veuillez saisir le numéro de transaction Mobile Money dans le champ de référence.';
                    title = 'Paiement par Mobile Money';
                    break;
            }
            
            // Mettre à jour le contenu et le style de la boîte d'information
            infoBox.css('border-left-color', borderColor);
            infoBox.html(`
                <h6 class="alert-heading d-flex align-items-center">
                    <i class="${iconClass} me-2"></i>
                    <span>${title}</span>
                </h6>
                <p class="mb-0">${infoContent}</p>
            `);
            
            // Ajouter une animation
            infoBox.css('opacity', '0');
            infoBox.animate({opacity: 1}, 300);
            
            // Mettre en évidence le champ de référence si nécessaire
            if (method !== 'cash') {
                $('#reference_number').addClass('border-primary').animate({borderWidth: '2px'}, 300).animate({borderWidth: '1px'}, 300);
                setTimeout(function() {
                    $('#reference_number').removeClass('border-primary');
                }, 1000);
            }
        }
        
        // Validation du formulaire
        $('#paymentForm').submit(function(e) {
            // Supprimer les anciennes alertes
            $('.alert-danger').remove();

            let method = $('input[name="payment_method"]:checked').val();
            let reference = $('#reference_number').val();
            let hasErrors = false;

            // Vérifier si une méthode de paiement est sélectionnée
            if (!method) {
                e.preventDefault();
                hasErrors = true;

                let alertHtml = `
                    <div class="alert alert-danger" style="border-radius: 12px; border-left: 4px solid var(--danger-color);">
                        <h6 class="alert-heading d-flex align-items-center">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <span>Méthode de paiement requise</span>
                        </h6>
                        <p class="mb-0">Veuillez sélectionner une méthode de paiement.</p>
                    </div>
                `;

                $('#paymentForm').before(alertHtml);

                // Mettre en évidence les cartes de paiement
                $('.payment-card').addClass('border-danger');
                setTimeout(function() {
                    $('.payment-card').removeClass('border-danger');
                }, 3000);
            }

            // Vérifier si une référence est requise pour certaines méthodes de paiement
            if (method && (method === 'bank_transfer' || method === 'check' || method === 'mobile_money') && reference === '') {
                e.preventDefault();
                hasErrors = true;

                let alertHtml = `
                    <div class="alert alert-danger" style="border-radius: 12px; border-left: 4px solid var(--danger-color);">
                        <h6 class="alert-heading d-flex align-items-center">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <span>Référence requise</span>
                        </h6>
                        <p class="mb-0">Veuillez saisir un numéro de référence pour ce mode de paiement.</p>
                    </div>
                `;

                $('#paymentForm').before(alertHtml);
                $('#reference_number').focus().addClass('is-invalid');
            }

            // Faire défiler jusqu'à l'alerte si il y a des erreurs
            if (hasErrors) {
                $('html, body').animate({
                    scrollTop: $('.alert-danger').offset().top - 100
                }, 500);

                // Supprimer les alertes après 5 secondes
                setTimeout(function() {
                    $('.alert-danger').fadeOut(500, function() {
                        $(this).remove();
                    });
                    $('#reference_number').removeClass('is-invalid');
                }, 5000);
            }
        });
        
        // Fonction pour vérifier et corriger la sélection
        function ensurePaymentMethodSelected() {
            const checkedRadio = document.querySelector('input[name="payment_method"]:checked');
            const selectedCard = document.querySelector('.payment-card.selected');

            console.log('Vérification de la sélection:', {
                checkedRadio: checkedRadio ? checkedRadio.value : 'aucun',
                selectedCard: selectedCard ? selectedCard.dataset.method : 'aucune'
            });

            if (!checkedRadio || !selectedCard) {
                console.log('Correction nécessaire - sélection de la méthode par défaut');
                // Forcer la sélection de la première carte (espèces)
                const firstCard = document.querySelector('.payment-card[data-method="cash"]');
                if (firstCard) {
                    firstCard.click();
                }
            }
        }

        // Initialiser la méthode de paiement par défaut
        setTimeout(function() {
            const defaultPaymentCard = document.querySelector('.payment-card input[type="radio"]:checked');
            if (defaultPaymentCard) {
                const defaultCard = defaultPaymentCard.closest('.payment-card');
                if (defaultCard) {
                    defaultCard.classList.add('selected');
                    const method = defaultCard.dataset.method;
                    if (method) {
                        updatePaymentMethodInfo(method);
                        toggleReferenceField(method);
                    }
                }
            } else {
                // Si aucune méthode n'est sélectionnée, sélectionner la première par défaut
                const firstCard = document.querySelector('.payment-card');
                if (firstCard) {
                    firstCard.click();
                }
            }

            // Vérifier après initialisation
            setTimeout(ensurePaymentMethodSelected, 500);
        }, 100);

        // Fonction de débogage accessible depuis la console
        window.debugPaymentSelection = function() {
            const cards = document.querySelectorAll('.payment-card');
            const radios = document.querySelectorAll('input[name="payment_method"]');
            const checkedRadio = document.querySelector('input[name="payment_method"]:checked');
            const selectedCard = document.querySelector('.payment-card.selected');

            console.log('=== DEBUG PAYMENT SELECTION ===');
            console.log('Cartes trouvées:', cards.length);
            console.log('Radio buttons trouvés:', radios.length);
            console.log('Radio button coché:', checkedRadio ? checkedRadio.value : 'aucun');
            console.log('Carte sélectionnée:', selectedCard ? selectedCard.dataset.method : 'aucune');

            radios.forEach((radio, index) => {
                console.log(`Radio ${index}:`, {
                    value: radio.value,
                    checked: radio.checked,
                    visible: radio.style.display !== 'none'
                });
            });

            cards.forEach((card, index) => {
                console.log(`Carte ${index}:`, {
                    method: card.dataset.method,
                    selected: card.classList.contains('selected'),
                    hasRadio: card.querySelector('input[type="radio"]') ? true : false
                });
            });
        };

        // Ajouter un gestionnaire pour vérifier périodiquement
        setInterval(function() {
            const checkedRadio = document.querySelector('input[name="payment_method"]:checked');
            if (!checkedRadio) {
                console.warn('Aucune méthode de paiement sélectionnée - correction automatique');
                ensurePaymentMethodSelected();
            }
        }, 5000);

        // Effet de survol pour les boutons
        $('.btn').hover(
            function() { $(this).css('transform', 'translateY(-2px)'); },
            function() { $(this).css('transform', 'translateY(0)'); }
        );

        console.log('Script de paiement initialisé. Utilisez debugPaymentSelection() pour déboguer.');
    });
</script>
@endsection
